import { Box, Stack, Typography } from "@mui/material";
import featured from '../../../assets/blog.png'
import person from '../../../assets/person.png'

export default function BlogCard() {
    return (
        <Box
            sx={{
                border: '1px solid rgba(0,0,0,0.1)',
                borderRadius: 2,
                overflow: 'hidden',
                transition: 'all 0.3s ease',
                cursor: 'pointer',
                '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 25px rgba(0,0,0,0.15)'
                }
            }}
        >
            <Box
                component='img'
                src={featured}
                sx={{
                    width: '100%',
                    height: { xs: '200px', md: '240px' },
                    objectFit: 'cover'
                }}
            />
            <Box sx={{ p: { xs: 2, md: 3 } }}>
                <Typography
                    sx={{
                        color: '#77829D',
                        fontWeight: 500,
                        mb: 2,
                        fontSize: { xs: '12px', md: '14px' }
                    }}
                >
                    Medical | March 31, 2022
                </Typography>
                <Typography
                    component='h3'
                    sx={{
                        color: '#1B3C74',
                        fontSize: { xs: '16px', md: '18px' },
                        fontWeight: 600,
                        lineHeight: 1.3,
                        mb: 3,
                        minHeight: { xs: '48px', md: '54px' }
                    }}
                >
                    6 Tips To Protect Your Mental Health When You're Sick
                </Typography>
                <Stack direction='row' spacing={1.5} alignItems='center'>
                    <Box
                        component='img'
                        src={person}
                        sx={{
                            height: { xs: 28, md: 32 },
                            width: { xs: 28, md: 32 },
                            borderRadius: '50%'
                        }}
                    />
                    <Typography
                        sx={{
                            color: '#1B3C74',
                            fontSize: { xs: '14px', md: '16px' },
                            fontWeight: 500
                        }}
                    >
                        Rebecca Lee
                    </Typography>
                </Stack>
            </Box>
        </Box>
    )
}